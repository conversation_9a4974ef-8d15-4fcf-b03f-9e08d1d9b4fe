import { ReactNode } from 'react';
import { HomeLayout } from "@/app/layouts/HomeLayout";
import { RequestInfo } from "rwsdk/worker";

import { TasksTableClient } from "@/app/components/TasksTableClient";

interface Task {
  id: number;
  title: string;
  description: string;
  details: string;
  testStrategy: string;
  priority: string;
  dependencies: number[];
  status: string;
  subtasks: any[]; // Define a more specific type if subtasks are structured
}

// Function to get tasks data - helps avoid hot-reload issues with JSON imports
function getTasksData(): Task[] {
  try {
    // Dynamic import to avoid hot-reload issues
    const tasksData = require('tasks/tasks.json');
    return tasksData.tasks || [];
  } catch (error) {
    console.error('Failed to load tasks data:', error);
    return [];
  }
}

export function TasksPage(props: RequestInfo & { children?: ReactNode }) {
  const tasks = getTasksData();

  return (
    <HomeLayout {...props}>
      <div className="container mx-auto py-10">
        <h1 className="page-title">Tasks</h1>
        <TasksTableClient tasks={tasks} />
      </div>
    </HomeLayout>
  );
}
