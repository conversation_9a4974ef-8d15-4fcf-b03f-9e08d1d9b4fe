import { ReactNode, Suspense } from 'react';
import { HomeLayout } from "@/app/layouts/HomeLayout";
import { RequestInfo } from "rwsdk/worker";

import { TasksTableClient } from "@/app/components/TasksTableClient";

// Assuming tasks.json is directly importable from the root or a specified path
import tasksData from 'tasks/tasks.json';

interface Task {
  id: number;
  title: string;
  description: string;
  details: string;
  testStrategy: string;
  priority: string;
  dependencies: number[];
  status: string;
  subtasks: any[]; // Define a more specific type if subtasks are structured
}

const tasks: Task[] = tasksData.tasks;

export function TasksPage(props: RequestInfo & { children?: ReactNode }) {
  return (
    <HomeLayout {...props}>
      <div className="px-6 w-full mx-auto py-10">
        <h1 className="page-title">Tasks</h1>
        <Suspense fallback={<div>Loading...</div>}>
          <TasksTableClient tasks={tasks} />
        </Suspense>
      </div>
    </HomeLayout>
  );
}
